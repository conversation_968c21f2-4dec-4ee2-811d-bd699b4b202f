#!/usr/bin/env python3
"""
Training Script for Vanna.AI with Ollama and MySQL
This script helps train the model with your database schema and sample data
"""

from dotenv import load_dotenv
load_dotenv()

import os
from vanna.ollama import Ollama
from vanna.chromadb import ChromaDB_VectorStore

class MyVanna(ChromaDB_VectorStore, Ollama):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        Ollama.__init__(self, config=config)

def main():
    print("🚀 Starting Vanna.AI Training Process...")
    
    # Initialize Vanna
    vn = MyVanna(config={
        'model': os.environ.get('OLLAMA_MODEL', 'mistral'),
        'ollama_host': os.environ.get('OLLAMA_HOST', 'http://localhost:11434')
    })
    
    # Connect to MySQL
    print("📊 Connecting to MySQL database...")
    try:
        vn.connect_to_mysql(
            host=os.environ.get('MYSQL_HOST', 'localhost'),
            dbname=os.environ['MYSQL_DATABASE'],
            user=os.environ['MYSQL_USER'],
            password=os.environ['MYSQL_PASSWORD'],
            port=int(os.environ.get('MYSQL_PORT', 3306))
        )
        print("✅ Successfully connected to MySQL!")
    except Exception as e:
        print(f"❌ Failed to connect to MySQL: {e}")
        return
    
    # Step 1: Auto-train from database schema
    print("\n📚 Step 1: Auto-training from database schema...")
    try:
        # Get information schema
        df_information_schema = vn.run_sql(f"""
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '{os.environ['MYSQL_DATABASE']}'
            ORDER BY TABLE_NAME, ORDINAL_POSITION
        """)
        
        print(f"Found {len(df_information_schema)} columns in database")
        
        # Generate and execute training plan
        plan = vn.get_training_plan_generic(df_information_schema)
        print("Generated training plan:")
        for item in plan:
            print(f"  - {item}")
        
        # Execute training
        vn.train(plan=plan)
        print("✅ Auto-training completed!")
        
    except Exception as e:
        print(f"❌ Auto-training failed: {e}")
    
    # Step 2: Add custom DDL statements
    print("\n🏗️  Step 2: Adding custom DDL statements...")
    
    # Get table creation statements
    try:
        tables_result = vn.run_sql(f"SHOW TABLES FROM {os.environ['MYSQL_DATABASE']}")
        tables = [row[0] for row in tables_result.values]
        
        for table in tables[:5]:  # Limit to first 5 tables
            try:
                ddl_result = vn.run_sql(f"SHOW CREATE TABLE {table}")
                ddl = ddl_result.iloc[0, 1]  # Get the CREATE TABLE statement
                
                vn.train(ddl=ddl)
                print(f"  ✅ Added DDL for table: {table}")
            except Exception as e:
                print(f"  ❌ Failed to get DDL for {table}: {e}")
                
    except Exception as e:
        print(f"❌ Failed to add DDL statements: {e}")
    
    # Step 3: Add sample documentation
    print("\n📖 Step 3: Adding documentation...")
    
    sample_docs = [
        "This database contains business data for analysis and reporting",
        "All date fields use YYYY-MM-DD format",
        "ID fields are primary keys and auto-increment",
        "Status fields typically contain values like 'active', 'inactive', 'pending'"
    ]
    
    for doc in sample_docs:
        try:
            vn.train(documentation=doc)
            print(f"  ✅ Added documentation: {doc}")
        except Exception as e:
            print(f"  ❌ Failed to add documentation: {e}")
    
    # Step 4: Add sample SQL queries
    print("\n🔍 Step 4: Adding sample SQL queries...")
    
    sample_queries = [
        "SELECT COUNT(*) as total_records FROM information_schema.tables WHERE table_schema = DATABASE()",
        f"SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = '{os.environ['MYSQL_DATABASE']}' ORDER BY table_rows DESC",
    ]
    
    for sql in sample_queries:
        try:
            vn.train(sql=sql)
            print(f"  ✅ Added SQL: {sql}")
        except Exception as e:
            print(f"  ❌ Failed to add SQL: {e}")
    
    # Step 5: Show training data summary
    print("\n📊 Training Summary:")
    try:
        training_data = vn.get_training_data()
        print(f"Total training items: {len(training_data)}")
        
        # Group by training type
        types = training_data['training_data_type'].value_counts()
        for training_type, count in types.items():
            print(f"  - {training_type}: {count} items")
            
    except Exception as e:
        print(f"❌ Failed to get training summary: {e}")
    
    # Step 6: Test the training
    print("\n🧪 Testing the trained model...")
    test_questions = [
        "How many tables are in the database?",
        "What are the names of all tables?",
        "Show me the structure of the first table"
    ]
    
    for question in test_questions:
        try:
            print(f"\nQ: {question}")
            sql = vn.generate_sql(question=question)
            print(f"Generated SQL: {sql}")
            
            # Optionally run the SQL
            # result = vn.run_sql(sql=sql)
            # print(f"Result: {result.head()}")
            
        except Exception as e:
            print(f"❌ Failed to generate SQL for '{question}': {e}")
    
    print("\n🎉 Training process completed!")
    print("\nNext steps:")
    print("1. Start your Flask app: python app.py")
    print("2. Open your browser to http://localhost:5000")
    print("3. Start asking questions about your database!")

if __name__ == "__main__":
    main()
