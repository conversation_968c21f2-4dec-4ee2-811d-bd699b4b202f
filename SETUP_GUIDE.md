# Vanna.AI Setup Guide with Ollama and MySQL

This guide will help you set up Vanna.AI with <PERSON>llama (local LLM) and MySQL database on AWS EC2 Ubuntu.

## Prerequisites Check

Before starting, verify these components are installed and running:

```bash
# Check Ollama
ollama --version
ollama list
ollama ps

# Check MySQL
sudo systemctl status mysql
mysql --version

# Check Python
python3 --version
pip3 --version
```

## Step 1: Install Dependencies

```bash
# Install the required Python packages
pip3 install 'vanna[chromadb,ollama,mysql]'
pip3 install flask python-dotenv

# Or use the requirements file
pip3 install -r requirements_mysql_ollama.txt
```

## Step 2: Setup Ollama

If Ollama is not installed:

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model (choose one)
ollama pull mistral        # Recommended for general use
ollama pull codellama      # Good for code generation
ollama pull llama2         # Alternative option

# Start Ollama service (if not running)
ollama serve
```

## Step 3: Configure Environment

1. Copy the environment template:
```bash
cp env_template.txt .env
```

2. Edit the `.env` file with your actual database credentials:
```bash
nano .env
```

Update these values:
- `MYSQL_DATABASE`: Your database name
- `MYSQL_USER`: Your MySQL username  
- `MYSQL_PASSWORD`: Your MySQL password
- `OLLAMA_MODEL`: The model you pulled (e.g., 'mistral')

## Step 4: Replace the Flask App

1. Backup your current app.py:
```bash
cp app.py app.py.backup
```

2. Replace with the modified version:
```bash
cp modified_app.py app.py
```

## Step 5: Train the Model

This is the crucial step that your client was asking about!

```bash
# Run the training script
python3 training_script.py
```

This script will:
- Connect to your MySQL database
- Automatically extract database schema
- Generate training data from table structures
- Add sample documentation
- Test the trained model

## Step 6: Start the Application

```bash
# Start the Flask app
python3 app.py
```

The application should start on `http://localhost:5000`

## Step 7: Test the Setup

1. Open your browser to `http://your-ec2-ip:5000`
2. Try asking questions like:
   - "How many tables are in the database?"
   - "What are the column names in the users table?"
   - "Show me the total count of records in each table"

## When to Use Ollama vs Vanna Cloud

### Use Ollama (Local) When:
- ✅ You want complete data privacy
- ✅ You have sensitive data that cannot leave your server
- ✅ You want to avoid API costs
- ✅ You have sufficient server resources (4GB+ RAM recommended)
- ✅ You want full control over the LLM

### Use Vanna Cloud When:
- ✅ You want faster setup
- ✅ You don't mind data being processed externally
- ✅ You want access to the latest models
- ✅ You have limited server resources

## Troubleshooting

### Ollama Issues
```bash
# Check if Ollama is running
ps aux | grep ollama

# Restart Ollama
pkill ollama
ollama serve

# Test Ollama directly
curl http://localhost:11434/api/generate -d '{
  "model": "mistral",
  "prompt": "Hello, how are you?"
}'
```

### MySQL Connection Issues
```bash
# Test MySQL connection
mysql -u your_user -p -h localhost your_database

# Check MySQL status
sudo systemctl status mysql
sudo systemctl restart mysql
```

### Flask App Issues
```bash
# Check if port 5000 is available
sudo netstat -tlnp | grep :5000

# Run Flask in debug mode
export FLASK_DEBUG=1
python3 app.py
```

### Training Issues
- Make sure your database has tables with data
- Verify the database user has SELECT permissions
- Check that the INFORMATION_SCHEMA is accessible

## Security Considerations for Production

1. **Change default ports**
2. **Use environment variables for all credentials**
3. **Set up proper firewall rules**
4. **Use HTTPS with SSL certificates**
5. **Implement authentication**
6. **Regular backups of training data**

## Next Steps

1. **Add more training data** specific to your business domain
2. **Create custom documentation** for your database tables
3. **Add example queries** that users commonly need
4. **Set up monitoring** and logging
5. **Configure automatic backups**

## Common Questions

**Q: How often should I retrain the model?**
A: Retrain when you add new tables, change schema, or want to improve query accuracy.

**Q: Can I use multiple databases?**
A: Yes, but you'll need separate Vanna instances for each database.

**Q: How do I add more training data?**
A: Use the `/api/v0/train` endpoint or modify the training_script.py

**Q: What if the generated SQL is wrong?**
A: Add the correct SQL as training data using the train endpoint.
